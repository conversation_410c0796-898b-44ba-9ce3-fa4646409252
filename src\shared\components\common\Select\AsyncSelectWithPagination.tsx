import React, { useState, useEffect, useRef, useCallback, forwardRef, useImperativeHandle } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { debounce } from 'lodash';
import { Loader2 } from 'lucide-react';
import { Icon } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';
import SelectOption from './SelectOption';
import type { SelectOption as SelectOptionType } from './Select';

export interface AsyncSelectWithPaginationProps {
  /**
   * Giá trị đã chọn
   */
  value?: string | string[] | number | number[];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: string | string[] | number | number[] | undefined) => void;

  /**
   * Hàm load options từ API với pagination
   */
  loadOptions: (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => Promise<{
    items: SelectOptionType[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
  }>;

  /**
   * Thời gian debounce cho search (ms)
   */
  debounceTime?: number;

  /**
   * Cho phép chọn nhiều
   */
  multiple?: boolean;

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Custom rendering
   */
  renderOption?: (option: SelectOptionType) => React.ReactNode;

  /**
   * Name attribute
   */
  name?: string;

  /**
   * ID attribute
   */
  id?: string;

  /**
   * CSS class
   */
  className?: string;

  /**
   * Error message
   */
  error?: string;

  /**
   * Helper text
   */
  helperText?: string;

  /**
   * Size
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Full width
   */
  fullWidth?: boolean;

  /**
   * Số items mỗi trang
   */
  itemsPerPage?: number;

  /**
   * Tự động load trang đầu tiên khi mở
   */
  autoLoadInitial?: boolean;

  /**
   * Message khi không có options
   */
  noOptionsMessage?: string;

  /**
   * Message khi đang loading
   */
  loadingMessage?: string;

  /**
   * Search chỉ khi nhấn Enter
   */
  searchOnEnter?: boolean;

  /**
   * Initial options để hiển thị selected value ngay từ đầu
   */
  initialOptions?: SelectOptionType[];
}

/**
 * Component AsyncSelectWithPagination - Select với khả năng tải dữ liệu từ API có phân trang
 */
const AsyncSelectWithPagination = forwardRef<HTMLInputElement, AsyncSelectWithPaginationProps>(
  (
    {
      value,
      onChange,
      loadOptions,
      debounceTime = 300,
      multiple = false,
      placeholder = '',
      label,
      disabled = false,
      renderOption,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      itemsPerPage = 20,
      autoLoadInitial = true,
      noOptionsMessage,
      loadingMessage,
      searchOnEnter = true,
      initialOptions = [],
    },
    ref
  ) => {
    useTheme(); // Keep the hook call to avoid React hooks rules violation
    const { t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);
    const [options, setOptions] = useState<SelectOptionType[]>([]);
    const [loading, setLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [pendingSearchTerm, setPendingSearchTerm] = useState('');
    const lastSearchTermRef = useRef('');
    const hasLoadedInitialRef = useRef(false);
    const [hasSearched, setHasSearched] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMore, setHasMore] = useState(false);
    const [selectedValues, setSelectedValues] = useState<(string | number)[]>(
      multiple
        ? Array.isArray(value)
          ? (value as (string | number)[])
          : []
        : value !== undefined
          ? [value as string | number]
          : []
    );
  // Cache để lưu trữ selected options để hiển thị label
  const [selectedOptionsCache, setSelectedOptionsCache] = useState<Map<string | number, SelectOptionType>>(new Map());
    const [dropdownPosition, setDropdownPosition] = useState<{
      top: number;
      left: number;
      width: number;
    } | null>(null);

    const selectRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);

    // Forward ref to hidden input element
    const hiddenInputRef = useRef<HTMLInputElement>(null);
    useImperativeHandle(ref, () => hiddenInputRef.current as HTMLInputElement);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10',
      lg: 'h-12 text-lg',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Update selectedValues when value prop changes
    useEffect(() => {
      if (multiple) {
        setSelectedValues(Array.isArray(value) ? (value as (string | number)[]) : []);
      } else {
        setSelectedValues(value !== undefined ? [value as string | number] : []);
      }
    }, [value, multiple]);

    // Initialize cache with initialOptions for selected values
    useEffect(() => {
      if (initialOptions.length > 0 && selectedValues.length > 0) {
        console.log('🔄 [AsyncSelectWithPagination] Initializing cache with:', {
          initialOptions: initialOptions.length,
          selectedValues,
          initialOptionsData: initialOptions
        });

        const newCache = new Map(selectedOptionsCache);
        let cacheUpdated = false;

        initialOptions.forEach(option => {
          if (selectedValues.includes(option.value)) {
            newCache.set(option.value, option);
            cacheUpdated = true;
            console.log('✅ [AsyncSelectWithPagination] Added to cache:', option);
          }
        });

        if (cacheUpdated) {
          setSelectedOptionsCache(newCache);
          console.log('🔄 [AsyncSelectWithPagination] Cache updated, total items:', newCache.size);
        }
      }
    }, [initialOptions, selectedValues]); // Không include selectedOptionsCache để tránh infinite loop

    // Load initial data when component mounts with value to ensure selected option is available
    useEffect(() => {
      if (autoLoadInitial && selectedValues.length > 0 && options.length === 0 && !hasLoadedInitialRef.current) {
        hasLoadedInitialRef.current = true;
        loadDataRef.current?.({ search: '', page: 1, reset: true });
      }
    }, [selectedValues, options.length, autoLoadInitial]);

    // Load data function - sử dụng useRef để tránh recreation
    const loadDataRef = useRef<(params: {
      search?: string;
      page?: number;
      reset?: boolean;
    }) => Promise<void>>();

    loadDataRef.current = async (params: {
      search?: string;
      page?: number;
      reset?: boolean;
    }) => {
      const { search = '', page = 1, reset = false } = params;

      // Tránh gọi API với cùng search term và page = 1 (trừ khi reset = true)
      // Chỉ áp dụng cho page = 1 (search mới), không áp dụng cho pagination
      if (page === 1 && !reset && search === lastSearchTermRef.current) {
        // console.log('🚫 Skipping duplicate API call for search:', search);
        return;
      }

      // Nếu đang loading và là cùng một search term, không gọi API
      if (loading && page === 1 && search === searchTerm) {
        // console.log('🚫 Skipping API call - already loading same search term:', search);
        return;
      }

      // Cập nhật last search term khi search mới (page = 1)
      if (page === 1) {
        lastSearchTermRef.current = search;
        // Mark that user has performed a search (including empty search)
        setHasSearched(true);
      }

      // console.log('🔍 Making API call:', { search, page, reset });
      setLoading(true);
      try {
        const result = await loadOptions({
          search,
          page,
          limit: itemsPerPage,
        });

        if (reset || page === 1) {
          setOptions(result.items);
        } else {
          setOptions(prev => [...prev, ...result.items]);
        }

        // Cache selected options để hiển thị label
        const newCache = new Map(selectedOptionsCache);
        result.items.forEach(item => {
          if (selectedValues.includes(item.value)) {
            newCache.set(item.value, item);
          }
        });
        setSelectedOptionsCache(newCache);

        setCurrentPage(result.currentPage);
        setHasMore(result.currentPage < result.totalPages);
      } catch (error) {
        console.error('Error loading options:', error);
        if (reset || page === 1) {
          setOptions([]);
        }
      } finally {
        setLoading(false);
      }
    };



    // Create debounced search function - tối ưu để tránh recreation
    const debouncedSearchRef = useRef<ReturnType<typeof debounce>>();

    // Tạo debounced function một lần và chỉ update khi cần thiết
    useEffect(() => {
      // Cancel previous debounced function
      debouncedSearchRef.current?.cancel();

      debouncedSearchRef.current = debounce((search: string) => {
        if (!searchOnEnter) {
          // Chỉ gọi API nếu search term thực sự thay đổi
          if (search !== searchTerm) {
            // console.log('🔍 Debounced search triggered:', search);
            setSearchTerm(search);
            setCurrentPage(1);
            loadDataRef.current?.({ search, page: 1, reset: true });
          } else {
            // console.log('🚫 Skipping debounced search - same term:', search);
          }
        }
      }, debounceTime);

      return () => {
        debouncedSearchRef.current?.cancel();
      };
    }, [debounceTime, searchOnEnter, searchTerm]); // Thêm searchTerm để kiểm tra

    // Handle search input change
    const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setPendingSearchTerm(value);

      if (!searchOnEnter && debouncedSearchRef.current) {
        debouncedSearchRef.current(value);
      }
    };

    // Handle search on Enter
    const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter' && searchOnEnter) {
        e.preventDefault();

        // Luôn gọi API khi user nhấn Enter, bất kể search term có trùng hay không
        // console.log('🔍 Enter search triggered:', pendingSearchTerm);
        setSearchTerm(pendingSearchTerm);
        setCurrentPage(1);
        // Reset lastSearchTermRef để force gọi API
        lastSearchTermRef.current = '';
        loadDataRef.current?.({ search: pendingSearchTerm, page: 1, reset: true });
      }
    };

    // Load more data when scrolling - tối ưu để tránh recreation
    const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

      if (
        scrollHeight - scrollTop <= clientHeight + 50 && // 50px threshold
        hasMore &&
        !loading
      ) {
        loadDataRef.current?.({ search: searchTerm, page: currentPage + 1 });
      }
    }, [hasMore, loading, searchTerm, currentPage]); // Loại bỏ loadData khỏi dependency

    // Calculate dropdown position when opening
    const calculateDropdownPosition = useCallback(() => {
      if (!selectRef.current) return null;

      const rect = selectRef.current.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      return {
        top: rect.bottom + scrollTop + 4, // 4px gap
        left: rect.left + scrollLeft,
        width: rect.width,
      };
    }, []);



    // Handle option click
    const handleOptionClick = (optionValue: string | number) => {
      let newSelectedValues: (string | number)[];

      // Tìm option được click để cache
      const clickedOption = options.find(opt => opt.value === optionValue);

      if (multiple) {
        // Toggle selection for multiple select
        if (selectedValues.includes(optionValue)) {
          newSelectedValues = selectedValues.filter(val => val !== optionValue);
          // Remove from cache khi bỏ chọn
          const newCache = new Map(selectedOptionsCache);
          newCache.delete(optionValue);
          setSelectedOptionsCache(newCache);
        } else {
          newSelectedValues = [...selectedValues, optionValue];
          // Add to cache khi chọn
          if (clickedOption) {
            const newCache = new Map(selectedOptionsCache);
            newCache.set(optionValue, clickedOption);
            setSelectedOptionsCache(newCache);
          }
        }
      } else {
        // Single select - thêm tính năng bỏ chọn
        if (selectedValues.includes(optionValue)) {
          // Nếu đang chọn option hiện tại thì bỏ chọn (deselect)
          newSelectedValues = [];
          // Clear cache khi bỏ chọn
          setSelectedOptionsCache(new Map());
        } else {
          // Chọn option mới
          newSelectedValues = [optionValue];
          // Update cache với option mới
          if (clickedOption) {
            const newCache = new Map();
            newCache.set(optionValue, clickedOption);
            setSelectedOptionsCache(newCache);
          }
        }
        setIsOpen(false); // Close dropdown for single select
      }

      setSelectedValues(newSelectedValues);

      // Call onChange with the new value(s)
      if (onChange) {
        if (multiple) {
          onChange(newSelectedValues as string[] | number[]);
        } else {
          // Trả về undefined khi bỏ chọn, hoặc giá trị khi chọn
          onChange(newSelectedValues.length > 0 ? newSelectedValues[0] : undefined);
        }
      }

      // Clear search input when option is selected và reset search
      setPendingSearchTerm('');

      // Reset search term về empty để load lại data ban đầu
      if (searchTerm !== '') {
        setSearchTerm('');
        setCurrentPage(1);
        lastSearchTermRef.current = '';
        loadDataRef.current?.({ search: '', page: 1, reset: true });
      }
    };

    // Get display value
    const getDisplayValue = () => {
      if (selectedValues.length === 0) return placeholder;

      if (multiple) {
        if (selectedValues.length === 1) {
          // Tìm trong options hiện tại trước, sau đó trong cache
          const firstValue = selectedValues[0];
          if (firstValue !== undefined) {
            let selectedOption = options.find(opt => opt.value === firstValue);
            if (!selectedOption) {
              selectedOption = selectedOptionsCache.get(firstValue);
            }
            return selectedOption ? selectedOption.label : firstValue.toString();
          }
          return '';
        } else {
          return t('common.selected', { count: selectedValues.length });
        }
      } else {
        // Tìm trong options hiện tại trước, sau đó trong cache
        const firstValue = selectedValues[0];
        if (firstValue !== undefined) {
          let selectedOption = options.find(opt => opt.value === firstValue);
          if (!selectedOption) {
            selectedOption = selectedOptionsCache.get(firstValue);
          }
          return selectedOption ? selectedOption.label : firstValue.toString();
        }
        return '';
      }
    };

    // Get appropriate no results message and icon based on current state
    const getNoResultsContent = () => {
      // If user has searched and there's a search term
      if (hasSearched && (searchTerm.trim() || pendingSearchTerm.trim())) {
        const currentSearchTerm = searchTerm.trim() || pendingSearchTerm.trim();
        return {
          icon: '🔍',
          message: t('common.noSearchResults', `Không tìm thấy kết quả cho "${currentSearchTerm}"`),
          subMessage: t('common.tryDifferentSearch', 'Thử tìm kiếm với từ khóa khác'),
          className: 'text-muted-foreground'
        };
      }

      // If user has searched but with empty term (showing all data but no results)
      if (hasSearched && !searchTerm.trim() && !pendingSearchTerm.trim()) {
        return {
          icon: '📭',
          message: t('common.noDataAvailable', 'Không có dữ liệu'),
          subMessage: t('common.noDataDescription', 'Hiện tại chưa có dữ liệu nào'),
          className: 'text-muted-foreground'
        };
      }

      // Default message (initial state or custom message)
      return {
        icon: '📋',
        message: noOptionsMessage || t('common.noResults', 'Không có kết quả'),
        subMessage: t('common.startTyping', 'Bắt đầu nhập để tìm kiếm'),
        className: 'text-muted-foreground'
      };
    };

    // Render single option
    const renderSingleOption = (option: SelectOptionType) => {
      const isSelected = selectedValues.includes(option.value);

      if (renderOption) {
        return (
          <div
            key={`option-${option.value}`}
            onClick={() => !option.disabled && handleOptionClick(option.value)}
            className={`${option.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          >
            {renderOption(option)}
          </div>
        );
      }

      return (
        <SelectOption
          key={`option-${option.value}`}
          value={option.value}
          label={option.label}
          icon={option.icon}
          disabled={option.disabled || false}
          selected={isSelected}
          onClick={() => handleOptionClick(option.value)}
          data={option.data}
        />
      );
    };

    // Close dropdown when clicking outside and handle scroll
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as Node;

        // Check if click is outside both select trigger and dropdown
        const isOutsideSelect = selectRef.current && !selectRef.current.contains(target);
        const isOutsideDropdown = !document.querySelector('.async-select-dropdown')?.contains(target);

        if (isOutsideSelect && isOutsideDropdown) {
          setIsOpen(false);
        }
      };

      const handleScroll = () => {
        if (isOpen) {
          const position = calculateDropdownPosition();
          setDropdownPosition(position);
        }
      };

      const handleResize = () => {
        if (isOpen) {
          const position = calculateDropdownPosition();
          setDropdownPosition(position);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleResize);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        window.removeEventListener('scroll', handleScroll, true);
        window.removeEventListener('resize', handleResize);
      };
    }, [isOpen, calculateDropdownPosition]);

    // Focus input when dropdown opens and calculate position
    useEffect(() => {
      if (isOpen) {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
        const position = calculateDropdownPosition();
        setDropdownPosition(position);

        // Load initial data if needed - chỉ load một lần
        if (autoLoadInitial && options.length === 0 && !hasLoadedInitialRef.current) {
          // console.log('🔄 Loading initial data');
          hasLoadedInitialRef.current = true;
          loadDataRef.current?.({ search: '', page: 1, reset: true });
        }
      } else {
        setDropdownPosition(null);
        // Reset hasLoadedInitialRef khi đóng dropdown để có thể load lại khi mở
        hasLoadedInitialRef.current = false;
        // Reset hasSearched khi đóng dropdown
        setHasSearched(false);
      }
    }, [isOpen, calculateDropdownPosition, autoLoadInitial, options.length]); // Loại bỏ loadData khỏi dependency



    return (
      <div className={`relative ${widthClass} ${className}`} ref={selectRef}>
        {/* Hidden input for form submission */}
        <input
          type="hidden"
          name={name}
          id={id}
          value={multiple ? selectedValues.join(',') : selectedValues[0] || ''}
          ref={hiddenInputRef}
        />

        {/* Label */}
        {label && <label className="block text-sm font-medium mb-1">{label}</label>}

        {/* Select trigger */}
        <div
          className={`
          flex items-center justify-between px-3
          border-0 rounded-md bg-card-muted text-foreground
          ${sizeClasses}
          ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
          ${error ? 'ring-1 ring-error' : ''}
          ${isOpen ? 'ring-2 ring-primary/30' : ''}
          ${fullWidth ? 'w-full' : ''}
        `}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <div className="flex-grow truncate">{getDisplayValue()}</div>

          <div className="flex items-center">
            {loading ? (
              <Icon name="loading" className="animate-spin" size="sm" />
            ) : (
              <svg
                className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            )}
          </div>
        </div>

        {/* Error message */}
        {error && (
          <p className="mt-1 text-sm text-destructive">{error}</p>
        )}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-muted-foreground">{helperText}</p>
        )}

        {/* Dropdown Portal */}
        {isOpen && dropdownPosition && createPortal(
          <div
            className="async-select-dropdown fixed z-[99999] bg-card rounded-md shadow-lg border-0 animate-fade-in"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              width: `${dropdownPosition.width}px`,
              maxHeight: '300px',
            }}
          >
            {/* Search input */}
            <div className="sticky top-0 p-2 bg-card border-b-0">
              <div className="relative">
                <input
                  ref={searchInputRef}
                  type="text"
                  value={pendingSearchTerm}
                  onChange={handleSearchInputChange}
                  onKeyDown={handleSearchKeyDown}
                  placeholder={
                    searchOnEnter
                      ? t('common.searchPressEnter', 'Tìm kiếm... (Nhấn Enter)')
                      : t('common.search', 'Tìm kiếm...')
                  }
                  className="w-full px-3 py-1 pr-8 text-sm border-0 rounded-md focus:outline-none focus:ring-1 focus:ring-primary/20 bg-card-muted text-foreground"
                  onClick={e => e.stopPropagation()}
                />
                {/* Clear search button */}
                {(pendingSearchTerm || searchTerm) && (
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setPendingSearchTerm('');
                      setSearchTerm('');
                      setCurrentPage(1);
                      lastSearchTermRef.current = '';
                      loadDataRef.current?.({ search: '', page: 1, reset: true });
                    }}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            </div>

            {/* Options */}
            <div
              className="max-h-60 overflow-auto custom-scrollbar auto-hide"
              role="listbox"
              aria-multiselectable={multiple}
              onScroll={handleScroll}
            >
              {loading && options.length === 0 ? (
                <div className="px-4 py-2 text-sm text-muted flex items-center">
                  <Icon name="loading" className="animate-spin mr-2" size="sm" />
                  {loadingMessage || t('common.loading', 'Loading...')}
                </div>
              ) : options.length > 0 ? (
                <>
                  {options.map(option => renderSingleOption(option))}

                  {/* Loading more indicator */}
                  {loading && options.length > 0 && (
                    <div className="flex items-center justify-center p-2 border-t">
                      <Loader2 size={14} className="animate-spin mr-2" />
                      <span className="text-xs text-muted-foreground">
                        {t('common.loadingMore', 'Đang tải thêm...')}
                      </span>
                    </div>
                  )}
                </>
              ) : (
                (() => {
                  const noResultsContent = getNoResultsContent();
                  return (
                    <div className={`px-4 py-4 text-sm ${noResultsContent.className} text-center`}>
                      <div className="flex flex-col items-center space-y-2">
                        <span className="text-2xl opacity-50">{noResultsContent.icon}</span>
                        <div className="space-y-1">
                          <div className="font-medium">{noResultsContent.message}</div>
                          {noResultsContent.subMessage && (
                            <div className="text-xs opacity-75">{noResultsContent.subMessage}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })()
              )}
            </div>
          </div>,
          document.body
        )}
      </div>
    );
  }
);

AsyncSelectWithPagination.displayName = 'AsyncSelectWithPagination';

export default AsyncSelectWithPagination;

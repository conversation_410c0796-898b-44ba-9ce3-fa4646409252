/**
 * Company Service
 * Service xử lý các API liên quan đến công ty
 */

import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { CreateCompanyDto, CreateCompanyResponseDto, CompanyDto } from '../types/company.types';

/**
 * Company Service Class
 */
export class CompanyService {
  private static readonly BASE_URL = '/company';

  /**
   * Tạo công ty mới
   * @param data Dữ liệu tạo công ty
   * @returns Promise với response từ API
   */
  static async createCompany(
    data: CreateCompanyDto
  ): Promise<ApiResponseDto<CreateCompanyResponseDto>> {
    try {
      // Validate required fields
      if (!data.full_name || data.full_name.trim() === '') {
        throw new Error('Tên đầy đủ công ty là bắt buộc');
      }

      if (!data.short_name || data.short_name.trim() === '') {
        throw new Error('Tên viết tắt công ty là bắt buộc');
      }

      // Validate length constraints
      if (data.full_name.length > 200) {
        throw new Error('Tên đầy đủ công ty không được vượt quá 200 ký tự');
      }

      if (data.short_name.length > 20) {
        throw new Error('Tên viết tắt công ty không được vượt quá 20 ký tự');
      }

      // Sanitize data
      const sanitizedData: CreateCompanyDto = {
        full_name: data.full_name.trim(),
        short_name: data.short_name.trim(),
      };

      const response = await apiClient.post<CreateCompanyResponseDto>(
        `${this.BASE_URL}/create`,
        sanitizedData
      );

      return response;
    } catch (error) {
      console.error('Error creating company:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin công ty theo ID
   * @param id ID công ty
   * @returns Promise với thông tin công ty
   */
  static async getCompany(id: string): Promise<ApiResponseDto<CompanyDto>> {
    try {
      const response = await apiClient.get<CompanyDto>(
        `${this.BASE_URL}/${id}`
      );
      return response;
    } catch (error) {
      console.error(`Error fetching company ${id}:`, error);
      throw error;
    }
  }
}

/**
 * Business logic layer cho Company
 */

/**
 * Tạo công ty với business logic
 * @param data Dữ liệu tạo công ty
 * @returns Promise với response từ API
 */
export const createCompanyWithBusinessLogic = async (
  data: CreateCompanyDto
): Promise<ApiResponseDto<CreateCompanyResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Additional validation
  // - Data transformation
  // - Logging
  // - Analytics tracking

  return CompanyService.createCompany(data);
};

/**
 * Lấy thông tin công ty với business logic
 * @param id ID công ty
 * @returns Promise với thông tin công ty
 */
export const getCompanyWithBusinessLogic = async (
  id: string
): Promise<ApiResponseDto<CompanyDto>> => {
  // Business logic có thể bao gồm:
  // - Cache logic
  // - Permission checks
  // - Data transformation

  return CompanyService.getCompany(id);
};

export { default as CustomerGeneralInfo } from './CustomerGeneralInfo';
export { default as CustomerOverview } from './CustomerOverview';
export { default as CustomerSocial } from './CustomerSocial';
export { default as CustomerCustomFields } from './CustomerCustomFields';
export { default as CustomerInteractions } from './CustomerInteractions';
export { default as CustomerOrders } from './CustomerOrders';
export { default as CustomerActivities } from './CustomerActivities';

// Product Form Sections
export { default as GeneralInfoSection } from './GeneralInfoSection';
export { default as PricingSection } from './PricingSection';
export { default as MediaSection } from './MediaSection';
export { default as InventorySection } from './InventorySection';
export { default as ShippingSection } from './ShippingSection';
export { default as ClassificationsSection } from './ClassificationsSection';
export { default as CustomFieldsSection } from './CustomFieldsSection';

// Digital Product Form Sections
export { default as DigitalPricingSection } from './DigitalPricingSection';
export { default as DigitalProcessingSection } from './DigitalProcessingSection';
export { default as DigitalOutputSection } from './DigitalOutputSection';
export { default as DigitalVersionsSection } from './DigitalVersionsSection';

// Service Product Form Sections
export { default as ServiceInfoSection } from './ServiceInfoSection';

// Combo Product Form Sections
export { default as ComboPricingSection } from './ComboPricingSection';
export { default as ComboProductsSection } from './ComboProductsSection';

// Event Product Form Sections
export { default as EventInfoSection } from './EventInfoSection';
export { default as EventTicketTypesSection } from './EventTicketTypesSection';



// Export types
export type {
  ProductInventory,
  ProductVariant,
  ExtendedFileWithMetadata,
  ProductFormValues,
  GeneralInfoSectionProps,
  PricingSectionProps,
  MediaSectionProps,
  InventorySectionProps,
  ShippingSectionProps,
  ClassificationsSectionProps,
  CustomFieldsSectionProps,
} from './product-form-types';

// Export types
export type {
  CustomerDetailData,
  CustomerMetadataField,
  CustomerInteraction,
  CustomerOrder,
  CustomerActivity,
  TopChannel,
  TopDevice
} from './types';

// Product Form Types
export type {
  ProductInventory,
  ProductVariant,
  ExtendedFileWithMetadata,
  ProductFormValues,
  SectionProps,
  GeneralInfoSectionProps,
  PricingSectionProps,
  MediaSectionProps,
  InventorySectionProps,
  ShippingSectionProps,
  ClassificationsSectionProps,
  CustomFieldsSectionProps
} from './product-form-types';

// Digital Product Form Types
export type {
  SelectedCustomField,
  FormDigitalProductVersion,
  DigitalProductFormValues,
  DigitalGeneralInfoSectionProps,
  DigitalPricingSectionProps,
  DigitalMediaSectionProps,
  DigitalProcessingSectionProps,
  DigitalOutputSectionProps,
  DigitalVersionsSectionProps,
  DigitalCustomFieldsSectionProps
} from './digital-product-form-types';

// Service Product Form Types
export type {
  ServiceProductFormValues,
  ServiceGeneralInfoSectionProps,
  ServicePricingSectionProps,
  ServiceInfoSectionProps,
  ServiceMediaSectionProps,
  ServiceCustomFieldsSectionProps
} from './service-product-form-types';

// Combo Product Form Types
export type {
  ComboProductItem,
  ComboProductFormValues,
  ComboGeneralInfoSectionProps,
  ComboPricingSectionProps,
  ComboProductsSectionProps,
  ComboMediaSectionProps,
  ComboCustomFieldsSectionProps
} from './combo-product-form-types';

// Event Product Form Types
export type {
  FormEventTicketType,
  EventProductFormValues,
  EventGeneralInfoSectionProps,
  EventInfoSectionProps,
  EventTicketTypesSectionProps,
  EventMediaSectionProps,
  EventCustomFieldsSectionProps
} from './event-product-form-types';

// Physical Product Edit Form Types
export type {
  PhysicalProductEditFormValues,
} from './physical-product-edit-form-types';

// Re-export types from ProductForm to avoid duplication
export type {
  SelectedCustomField,
  ExtendedFileWithMetadata,
  ProductVariant,
  ProductInventory
} from '../ProductForm';



// Interface cho form values
export interface PhysicalProductEditFormValues {
  name: string;
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    lengthCm?: string | number;
    widthCm?: string | number;
    heightCm?: string | number;
    weightGram?: string | number;
  };
  inventory?: ProductInventory[];
  customFields?: SelectedCustomField[];
  media?: ExtendedFileWithMetadata[];
  classifications?: ProductVariant[];
}

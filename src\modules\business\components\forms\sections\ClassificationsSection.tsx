import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  Textarea,
  Select,
  IconCard,
} from '@/shared/components/common';
import SimpleCustomFieldSelector, { CustomFieldData } from '@/modules/business/components/SimpleCustomFieldSelector';
import CustomFieldRenderer from '@/modules/business/components/CustomFieldRenderer';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { ClassificationsSectionProps, ExtendedFileWithMetadata } from './product-form-types';

// Interface cho selected custom field
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

const ClassificationsSection: React.FC<ClassificationsSectionProps> = ({
  productClassifications,
  handleAddVariant,
  handleRemoveVariant,
  handleUpdateVariant,
  handleVariantImagesChange,
  handleToggleCustomFieldToVariant,
  handleUpdateCustomFieldInVariant,
  handleRemoveCustomFieldFromVariant,
}) => {
  const { t } = useTranslation(['business', 'common']);
  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="font-medium">
            {productClassifications.length > 0
              ? `${t('business:product.form.sections.variants', '6. Mẫu mã')} (${productClassifications.length})`
              : t('business:product.form.sections.variants', '6. Mẫu mã')
            }
          </Typography>
          <div
            onClick={e => {
              e.preventDefault();
              e.stopPropagation(); // Ngăn không cho toggle card
            }}
          >
            <IconCard
              icon="plus"
              variant="primary"
              size="sm"
              title={t('business:product.form.variants.addVariant', 'Thêm biến thể')}
              onClick={handleAddVariant}
            />
          </div>
        </div>
      }
      defaultOpen={false}
      className="mb-4"
    >
      {/* Danh sách phân loại (đổi tên từ biến thể) */}
      {productClassifications.length > 0 ? (
        <div className="space-y-4">
          {productClassifications.map((variant, index) => (
            <CollapsibleCard
              key={variant.id}
              title={
                <div className="flex justify-between items-center w-full">
                  <div className="flex items-center space-x-4">
                    <Typography variant="body2" className="font-medium">
                      {variant.name || `${t('business:product.form.variants.variant', 'Phân loại')} ${index + 1}`}
                    </Typography>
                    {variant.listPrice && variant.salePrice && (
                      <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                        {Number(variant.salePrice) > 0 ? `${Number(variant.salePrice).toLocaleString()} ${variant.currency}` : '0 VND'}
                      </Typography>
                    )}
                    {variant.availableQuantity && (
                      <Typography variant="body2" className="text-gray-500 dark:text-gray-500">
                        {variant.availableQuantity} sản phẩm
                      </Typography>
                    )}
                    {variant.id && (
                      <Typography variant="body2" className="text-gray-400 dark:text-gray-600 text-xs">
                        ID: {variant.id.toString().slice(-4)}
                      </Typography>
                    )}
                  </div>
                  <div
                    onClick={e => {
                      e.stopPropagation();
                      handleRemoveVariant(variant.id);
                    }}
                    className="cursor-pointer"
                  >
                    <IconCard
                      icon="trash"
                      variant="danger"
                      size="sm"
                      title={t('common:delete', 'Xóa')}
                    />
                  </div>
                </div>
              }
              defaultOpen={index === 0}
            >
              {/* Thông tin cơ bản */}
              <div className="mb-4">
                <Typography
                  variant="subtitle2"
                  className="mb-2 font-medium text-gray-700 dark:text-gray-300"
                >
                  {t('business:product.form.variants.basicInfo', 'Thông tin cơ bản')}
                </Typography>
                <div className="space-y-3">
                  {/* Tên và mô tả biến thể */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItem label={t('business:product.form.variants.name', 'Tên biến thể')}>
                      <Input
                        fullWidth
                        value={variant.name}
                        onChange={e => handleUpdateVariant(variant.id, 'name', e.target.value)}
                        placeholder={t(
                          'business:product.form.variants.namePlaceholder',
                          'Nhập tên biến thể'
                        )}
                      />
                    </FormItem>

                    <FormItem label={t('business:product.form.variants.description', 'Mô tả')}>
                      <Textarea
                        fullWidth
                        rows={2}
                        value={variant.description || ''}
                        onChange={e => handleUpdateVariant(variant.id, 'description', e.target.value)}
                        placeholder={t(
                          'business:product.form.variants.descriptionPlaceholder',
                          'Nhập mô tả cho biến thể này'
                        )}
                      />
                    </FormItem>
                  </div>

                  {/* Các trường giá cố định */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormItem
                      label={t('business:product.form.variants.currency', 'Đơn vị tiền tệ')}
                    >
                      <Select
                        fullWidth
                        value={variant.currency}
                        onChange={val =>
                          handleUpdateVariant(variant.id, 'currency', val as string)
                        }
                        options={[
                          { value: 'VND', label: 'VND' },
                          { value: 'USD', label: 'USD' },
                          { value: 'EUR', label: 'EUR' },
                        ]}
                      />
                    </FormItem>

                    <FormItem
                      label={t('business:product.form.variants.listPrice', 'Giá niêm yết')}
                    >
                      <Input
                        fullWidth
                        type="number"
                        min="0"
                        value={variant.listPrice}
                        onChange={e =>
                          handleUpdateVariant(variant.id, 'listPrice', e.target.value)
                        }
                        placeholder="0"
                      />
                    </FormItem>

                    <FormItem label={t('business:product.form.variants.salePrice', 'Giá bán')}>
                      <Input
                        fullWidth
                        type="number"
                        min="0"
                        value={variant.salePrice}
                        onChange={e =>
                          handleUpdateVariant(variant.id, 'salePrice', e.target.value)
                        }
                        placeholder="0"
                      />
                    </FormItem>
                  </div>
                </div>
              </div>

              {/* ✅ Quản lý tồn kho và ảnh cho biến thể */}
              <div className="mb-4">
                <Typography
                  variant="subtitle2"
                  className="mb-2 font-medium text-gray-700 dark:text-gray-300"
                >
                  {t('business:product.form.variants.inventory', 'Quản lý tồn kho & Ảnh')}
                </Typography>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <FormItem label={t('business:product.form.variants.sku', 'SKU biến thể')}>
                    <Input
                      fullWidth
                      value={variant.sku || ''}
                      onChange={e => handleUpdateVariant(variant.id, 'sku', e.target.value)}
                      placeholder={t(
                        'business:product.form.variants.skuPlaceholder',
                        'SKU-VARIANT-001'
                      )}
                    />
                  </FormItem>

                  <FormItem
                    label={t(
                      'business:product.form.variants.availableQuantity',
                      'Số lượng có sẵn'
                    )}
                    helpText={t(
                      'business:product.form.variants.availableQuantityHelper',
                      'Số lượng hiện có trong kho'
                    )}
                  >
                    <Input
                      fullWidth
                      type="number"
                      min="0"
                      value={variant.availableQuantity || ''}
                      onChange={e =>
                        handleUpdateVariant(variant.id, 'availableQuantity', e.target.value)
                      }
                      placeholder="0"
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.variants.images', 'Ảnh biến thể')}
                    helpText={t(
                      'business:product.form.variants.imagesHelper',
                      'Tải lên nhiều ảnh cho biến thể này'
                    )}
                  >
                    <MultiFileUpload
                      value={variant.images || []}
                      onChange={(files: FileWithMetadata[]) => {
                        // Convert FileWithMetadata to ExtendedFileWithMetadata
                        const extendedFiles: ExtendedFileWithMetadata[] = files.map(file => ({
                          ...file,
                          url: file.preview || undefined, // Use preview as URL since FileWithMetadata doesn't have url
                          name: file.file.name || undefined,
                          size: file.file.size || undefined,
                          type: file.file.type || undefined,
                        }));
                        handleVariantImagesChange(variant.id, extendedFiles);
                      }}
                      accept="image/*"
                      mediaOnly={true}
                      placeholder={t(
                        'business:product.form.variants.imagesPlaceholder',
                        'Chọn ảnh cho biến thể'
                      )}
                      className="w-full"
                    />
                  </FormItem>
                </div>
              </div>

              {/* Trường tùy chỉnh cho biến thể */}
              <div className="mb-3">
                <Typography
                  variant="subtitle2"
                  className="mb-2 font-medium text-gray-700 dark:text-gray-300"
                >
                  {t('business:product.form.variants.customFields', 'Thuộc tính biến thể')}
                </Typography>

                <SimpleCustomFieldSelector
                  onFieldSelect={(fieldData: CustomFieldData) => {
                    handleToggleCustomFieldToVariant(
                      variant.id,
                      fieldData.id,
                      fieldData as unknown as Record<string, unknown>
                    );
                  }}
                  selectedFieldIds={variant.customFields.map((f: SelectedCustomField) => f.fieldId)}
                  placeholder={t(
                    'business:product.form.variants.searchCustomField',
                    'Nhập từ khóa và nhấn Enter để tìm thuộc tính...'
                  )}
                />

                {variant.customFields.length > 0 && (
                  <div className="space-y-2">
                    {variant.customFields.map((field: SelectedCustomField) => (
                      <CustomFieldRenderer
                        key={field.id}
                        field={field}
                        value={(field.value?.['value'] as string) || ''}
                        onChange={value =>
                          handleUpdateCustomFieldInVariant(
                            variant.id,
                            field.id,
                            value as string
                          )
                        }
                        onRemove={() =>
                          handleRemoveCustomFieldFromVariant(variant.id, field.id)
                        }
                      />
                    ))}
                  </div>
                )}
              </div>
            </CollapsibleCard>
          ))}
        </div>
      ) : (
        <div className="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
            {t(
              'business:product.form.variants.noVariants',
              'Chưa có biến thể nào. Nhấn "Thêm biến thể" để bắt đầu.'
            )}
          </Typography>
        </div>
      )}
    </CollapsibleCard>
  );
};

export default ClassificationsSection;

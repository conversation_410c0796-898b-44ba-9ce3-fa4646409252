/**
 * Company Create Form Component
 * Form tạo công ty mới
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  Form,
  FormItem,
  Input,
  Button,
  Card,
  Typography,
} from '@/shared/components/common';
import { useApiForm } from '@/shared/hooks';
import { NotificationUtil } from '@/shared/utils';
import { useCreateCompany } from '../../hooks/useCompany';
import { CreateCompanyDto } from '../../types/company.types';

/**
 * Props cho CompanyCreateForm
 */
interface CompanyCreateFormProps {
  /** Callback khi tạo thành công */
  onSuccess?: (companyId: string) => void;
  /** Callback khi hủy */
  onCancel?: () => void;
  /** Class name tùy chỉnh */
  className?: string;
}

/**
 * Company Create Form Component
 */
const CompanyCreateForm: React.FC<CompanyCreateFormProps> = ({
  onSuccess,
  onCancel,
  className,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const createCompanyMutation = useCreateCompany();

  // Schema validation với Zod
  const companySchema = z.object({
    full_name: z
      .string()
      .min(1, t('business:company.validation.fullNameRequired'))
      .max(200, t('business:company.validation.fullNameMaxLength', { max: 200 })),
    short_name: z
      .string()
      .min(1, t('business:company.validation.shortNameRequired'))
      .max(20, t('business:company.validation.shortNameMaxLength', { max: 20 })),
  });

  // Sử dụng useApiForm hook
  const { formRef, handleSubmit, isSubmitting } = useApiForm<CreateCompanyDto>({
    apiCall: async (data: CreateCompanyDto) => {
      const result = await createCompanyMutation.mutateAsync(data);
      return result;
    },
    onSuccess: (data) => {
      NotificationUtil.success(
        data.result?.message || t('business:company.messages.createSuccess')
      );
      
      // Gọi callback onSuccess với company ID
      if (data.result?.id && onSuccess) {
        onSuccess(data.result.id);
      }
    },
    onError: (errors, message) => {
      NotificationUtil.error(
        message || t('business:company.messages.createError')
      );
    },
    resetOnSuccess: true,
  });

  return (
    <div className={`w-full bg-background text-foreground ${className || ''}`}>
      <Card>
        <div className="space-y-6">
          {/* Header */}
          <div className="space-y-2">
            <Typography variant="h2">
              {t('business:company.createTitle')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('business:company.createDescription')}
            </Typography>
          </div>

          {/* Form */}
          <Form
            ref={formRef}
            schema={companySchema}
            onSubmit={handleSubmit}
            mode="onSubmit"
            validateOnChange={false}
            validateOnBlur={true}
          >
            <div className="space-y-4">
              {/* Tên đầy đủ công ty */}
              <FormItem
                name="full_name"
                label={t('business:company.form.fullName')}
                required
              >
                <Input
                  placeholder={t('business:company.form.fullNamePlaceholder')}
                  fullWidth
                  maxLength={200}
                />
              </FormItem>

              {/* Tên viết tắt công ty */}
              <FormItem
                name="short_name"
                label={t('business:company.form.shortName')}
                required
              >
                <Input
                  placeholder={t('business:company.form.shortNamePlaceholder')}
                  fullWidth
                  maxLength={20}
                />
              </FormItem>

              {/* Action buttons */}
              <div className="flex justify-end space-x-3 pt-4">
                {onCancel && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                    disabled={isSubmitting}
                  >
                    {t('business:company.form.cancel')}
                  </Button>
                )}
                <Button
                  type="submit"
                  variant="primary"
                  loading={isSubmitting}
                  disabled={isSubmitting}
                >
                  {isSubmitting
                    ? t('business:company.form.submitting')
                    : t('business:company.form.submit')}
                </Button>
              </div>
            </div>
          </Form>
        </div>
      </Card>
    </div>
  );
};

export default CompanyCreateForm;
